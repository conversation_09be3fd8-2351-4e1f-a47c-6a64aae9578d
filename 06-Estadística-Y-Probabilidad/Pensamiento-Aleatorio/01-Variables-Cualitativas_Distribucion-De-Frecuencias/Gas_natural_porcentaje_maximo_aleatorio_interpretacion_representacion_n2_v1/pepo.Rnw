\documentclass{article}
\usepackage[utf8]{inputenc}
\usepackage[spanish]{babel}
\usepackage{graphicx}
\usepackage{float}

\title{Ejemplo de integración R y Python}
\author{Tu nombre}
\date{\today}

\begin{document}

\maketitle

\section{Introducción}

Este documento demuestra cómo integrar código de R y Python usando knitr y reticulate.

<<setup, include=FALSE>>=
library(reticulate)
library(knitr)

# Configurar Python (ajusta la ruta según tu sistema)
use_python("/usr/bin/python3")  # Linux/Mac
# use_python("C:/Python39/python.exe")  # Windows

# Configurar knitr para manejar figuras
opts_chunk$set(
  echo = TRUE,
  warning = FALSE,
  message = FALSE,
  fig.path = 'figure/',
  fig.align = 'center',
  fig.show = 'hold',
  fig.pos = 'H'
)

# Verificar que Python esté disponible
cat("Configuración de Python:\n")
py_config()
@

\section{Gráfico con Python}

El siguiente código genera un gráfico de la función seno usando Python:

<<python-chunk, engine='python', fig.cap='Gráfico de la función seno', fig.height=5, fig.width=8>>=
import numpy as np
import matplotlib.pyplot as plt

# Configurar matplotlib para usar backend no interactivo
import matplotlib
matplotlib.use('Agg')

# Generar datos
x = np.linspace(0, 10, 100)
y = np.sin(x)

# Crear el gráfico
plt.figure(figsize=(10, 6))
plt.plot(x, y, 'b-', linewidth=2, label='sin(x)')
plt.xlabel('x', fontsize=12)
plt.ylabel('sin(x)', fontsize=12)
plt.title('Función Seno', fontsize=14, fontweight='bold')
plt.grid(True, alpha=0.3)
plt.legend()

# Guardar y mostrar
plt.tight_layout()
plt.savefig('python-chunk-1.png', dpi=150, bbox_inches='tight')
plt.show()
@

\section{Análisis con R}

Ahora podemos acceder a los datos de Python desde R:

<<r-analysis, echo=TRUE>>=
# Acceder a las variables de Python
x_from_python <- py$x
y_from_python <- py$y

# Mostrar estadísticas básicas
cat("Estadísticas de x:\n")
summary(x_from_python)

cat("\nEstadísticas de y (sin(x)):\n")
summary(y_from_python)

# Crear un gráfico similar con R
plot(x_from_python, y_from_python,
     type = "l",
     col = "red",
     lwd = 2,
     main = "Función Seno (generado con R)",
     xlab = "x",
     ylab = "sin(x)")
grid()
@

\section{Conclusión}

Este ejemplo muestra cómo:
\begin{itemize}
\item Configurar reticulate para usar Python en documentos R Noweb
\item Generar gráficos con matplotlib en Python
\item Intercambiar datos entre R y Python
\item Crear un documento PDF integrado
\end{itemize}

\end{document}
